import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IConfirmPaymentParams,
    IRejectPaymentParams,
    IUploadInvoiceParams,
    IUploadAttachmentParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 查询服务商所有的结算单信息
    getBillList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/payment/getBillList', params)
    },
    // 付款单列表查询
    getPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/payment/getPage', params)
    },
    // 付款单详情查询
    getDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/plat/payment/getDetails', {
            id
        })
    },
    // 创建付款单
    create: (params: ICreatePaymentFromParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/payment/create', params)
    },
    // 财务确认付款并上传付款凭证
    confirmPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/payment/confirm/payment', params)
    },
    // 财务驳回
    rejectPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/payment/reject/payment', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/paymentFrom/delete', { id })
    },

    // 查询服务商所有的结算单信息-缴费
    getBalnceList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getBalanceList', params)
    },
    // 收款单列表查询-缴费
    getPaymentPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getPage', params)
    },

    // 服务商缴费单列表查询
    getMerchantPaymentPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/merchant/receive/payment/getPage', params)
    },
    // 服务商缴费单详情查询
    getMerchantPaymentDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/merchant/receive/payment/getDetails', {
            id
        })
    },
    // 服务商上传支付凭证
    uploadPaymentAttachment: (params: IUploadAttachmentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/receive/payment/upload/paymentVoucher', params)
    },
    // 收款单详情查询-缴费
    getPaymentDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getDetails', {
            id
        })
    },
    // 创建付款单-缴费
    createPayment: (params: ICreatePaymentFromParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/create', params)
    },
    // 财务确认付款并上传付款凭证-缴费
    confirmPaymentPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/confirm/receivePayment', params)
    },
   
    // 上传发票-缴费
    uploadInvoice: (params: IUploadInvoiceParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/upload/invoice', params)
    },
    // 删除收款单-缴费
    removePayment: (id: number): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/delete', { id })
    },


    getPayMentList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getPage', params)
    },


    // 收款单列表查询
    getMerchantPayMentList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/merchant/payment/getPage', params)
    },
    // 查询付款单详情
    getMerchantDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/merchant/payment/getDetails', {
            id
        })
    },

    // 供应商上传发票
    uploadPaymentInvoice: (params: IUploadInvoiceParams): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/payment/upload/invoice', params)
    },

}
